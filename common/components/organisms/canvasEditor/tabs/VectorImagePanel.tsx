'use client'

import React, {
  useEffect, useRef, useState,
} from 'react';
import Konva from 'konva';
import autosize from 'autosize';
import { VectorImageStyleGroups } from '@/common/constants';
import toast from 'react-hot-toast';
import {
  ArrowLeft, Download,
} from 'lucide-react';

import { useMixpanelEvent } from '@/common/utils/mixpanel/eventTriggers';
import { useProjectContext } from '@/common/contexts/ProjectContext';
import { projectImageStorage } from '@/common/utils/projectImageStorage';
import {
  Button, TextArea,
} from '../../../atoms';
import { addCursorHandlers } from '../CanvasEditor';

interface VectorImagePanelProps {
  canvas: Konva.Stage | null;
  agentId?: string;
  planId?: string;
  containerRef?: React.RefObject<HTMLDivElement>;
  zoomLevel?: number;
}



export const VectorImagePanel = ({
  canvas,
  agentId,
  planId,
  containerRef,
  zoomLevel,
}: VectorImagePanelProps) => {
  const imagePromptRef = useRef<HTMLTextAreaElement>(null);
  const [imagePrompt, setImagePrompt] = useState('');
  const [selectedStyle, setSelectedStyle] = useState<typeof VectorImageStyleGroups.none.styles[0] | null>(null);
  const [seed, setSeed] = useState(Math.floor(Math.random() * 1000000));
  const [guidanceScale, setGuidanceScale] = useState(3.5);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState('');
  const [currentStep, setCurrentStep] = useState<'style' | 'details'>('style');
  const [svgPreview, setSvgPreview] = useState<string | null>(null);
  const { trackContentEvent } = useMixpanelEvent();
  const { activeProject } = useProjectContext();

  useEffect(() => {
    if (imagePromptRef?.current) {
      autosize(imagePromptRef.current);
    }
  }, []);

  const handleStyleSelect = (style: typeof VectorImageStyleGroups.none.styles[0]) => {
    setSelectedStyle(style);
    setCurrentStep('details');
  };

  const handleBackToStyles = () => {
    setCurrentStep('style');
  };

  const handleGenerate = async () => {
    if (!imagePrompt.trim()) {
      setError('Please enter an image description');
      return;
    }

    if (imagePrompt.length < 3) {
      setError('Description should be at least 3 characters');
      return;
    }

    if (!agentId) {
      setError('Agent ID is required for image generation');
      return;
    }

    setError('');
    setIsGenerating(true);

    try {
      const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
      const endpoint = `${baseUrl}/${agentId}/recraft-image`;

      // Build the prompt with style if selected
      let finalPrompt = imagePrompt;
      if (selectedStyle && selectedStyle.option !== 'none') {
        // Add style-specific prompt enhancement based on the selected style
        const stylePrompts: Record<string, string> = {
          minimalistLogo: 'minimalist logo design, clean simple shapes, transparent background, vector graphics',
          geometricLogo: 'geometric logo design, abstract shapes, transparent background, vector art, bold geometric forms',
          flatDesign: 'flat design style, simple clean graphics, transparent background, vector illustration, minimal shadows',
          iconDesign: 'icon design, simple symbolic graphics, transparent background, vector format, clear recognizable shapes',
          badge: 'badge design, emblem style, transparent background, vector graphics, circular or shield shape',
          emblem: 'emblem design, heraldic style, transparent background, vector art, symmetrical composition',
          vectorIllustration: 'vector illustration, clean lines, bold colors, transparent background, scalable graphics',
          lineArt: 'line art illustration, clean black lines, transparent background, vector graphics, minimal style',
          silhouette: 'silhouette design, solid black shapes, transparent background, vector graphics, recognizable forms',
          stencilArt: 'stencil art design, cut-out style, transparent background, vector graphics, bold shapes',
          technicalIllustration: 'technical illustration, precise diagrams, transparent background, vector graphics, detailed schematics',
          isometricDrawing: 'isometric drawing, 3D perspective, transparent background, vector graphics, geometric precision',
          geometricAbstraction: 'geometric abstraction, abstract shapes, transparent background, vector art, mathematical patterns',
          mandala: 'mandala design, intricate symmetrical patterns, transparent background, vector graphics, geometric design',
          kaleidoscopic: 'kaleidoscopic pattern, symmetrical design, transparent background, vector graphics, radial symmetry',
          cubism: 'cubist style, geometric forms, transparent background, vector art, abstract representation',
          modernTypography: 'modern typography design, clean sans-serif fonts, transparent background, vector graphics, minimalist text',
          handLettering: 'hand lettering design, custom typography, transparent background, vector graphics, artistic text',
          calligraphy: 'calligraphy design, elegant script, transparent background, vector graphics, flowing letterforms',
          kinetic: 'kinetic typography, dynamic text, transparent background, vector graphics, motion-inspired design',
          vintage: 'vintage typography, retro lettering, transparent background, vector graphics, classic design elements',
        };

        const stylePrompt = stylePrompts[selectedStyle.option];
        if (stylePrompt) {
          finalPrompt = `${imagePrompt}, ${stylePrompt}`;
        }
      }

      const requestBody = {
        prompt: finalPrompt,
        width: 1024,
        height: 1024,
        count: 1,
        model: 'recraftv3',
        style: 'vector_illustration', // Use Recraft's vector illustration style
        seed: seed,
        controls: {
          artistic_level: Math.round((guidanceScale - 1) * 5 / 9), // Convert guidance scale (1-10) to artistic level (0-5)
          no_text: false, // Allow text in vector designs
        },
      };

      const response = await fetch(endpoint, {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (response.status === 422 && errorData.error?.includes('NSFW')) {
          throw new Error('Content flagged as inappropriate. Please try a different prompt.');
        }
        throw new Error(errorData.error || 'Failed to generate vector image');
      }

      const result = await response.json();

      if (result.success && result.images && result.images.length > 0) {
        const imageUrl = result.images[0];

        // Since recraft-image with vector_illustration style already returns SVG, use it directly
        const svgResponse = await fetch(imageUrl);
        const svgString = await svgResponse.text();

        setSvgPreview(svgString);

        // Add to canvas
        await addSvgToCanvas(svgString);

        trackContentEvent('image', {
          prompt: imagePrompt,
          imageStyle: selectedStyle?.option || 'none',
        });

        toast.success('Vector image generated and added to canvas!');
      } else {
        throw new Error('No image data received from Recraft');
      }
    } catch (error: unknown) {
      console.error('Error generating vector image:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to generate vector image. Please try again.';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsGenerating(false);
    }
  };





  const addSvgToCanvas = async (svgString: string) => {
    if (!canvas) {
      return;
    }

    try {
      // Create a blob from the SVG string
      const svgBlob = new Blob([svgString], { type: 'image/svg+xml' });
      const svgUrl = URL.createObjectURL(svgBlob);

      // Convert SVG to image for Konva
      const imageObj = new window.Image();
      imageObj.onload = () => {
        const konvaImage = new Konva.Image({
          image: imageObj,
          draggable: true,
        });

        // Add cursor handlers to the vector image
        addCursorHandlers(konvaImage);

        // Scale to fit canvas if needed
        if (containerRef?.current) {
          const containerRect = containerRef.current.getBoundingClientRect();
          const maxWidth = (containerRect.width * 0.8) / (zoomLevel || 1);
          const maxHeight = (containerRect.height * 0.8) / (zoomLevel || 1);

          const imageWidth = imageObj.naturalWidth;
          const imageHeight = imageObj.naturalHeight;

          if (imageWidth > maxWidth || imageHeight > maxHeight) {
            const scale = Math.min(maxWidth / imageWidth, maxHeight / imageHeight);
            konvaImage.scaleX(scale);
            konvaImage.scaleY(scale);
          }
        }

        // Get or create the main layer
        let layer = canvas.findOne('Layer') as Konva.Layer;
        if (!layer) {
          layer = new Konva.Layer();
          canvas.add(layer);
        }

        // Center the image
        const canvasWidth = canvas.width();
        const canvasHeight = canvas.height();
        konvaImage.x((canvasWidth - konvaImage.width() * konvaImage.scaleX()) / 2);
        konvaImage.y((canvasHeight - konvaImage.height() * konvaImage.scaleY()) / 2);

        layer.add(konvaImage);

        // Find or create transformer - use the main layer for the transformer
        const mainLayer = canvas.findOne('Layer') as Konva.Layer;
        if (mainLayer) {
          let transformer = canvas.findOne('Transformer') as Konva.Transformer;
          if (!transformer) {
            transformer = new Konva.Transformer();
            mainLayer.add(transformer);
          }

          transformer.nodes([konvaImage]);
        }
        canvas.batchDraw();

        // Clean up the blob URL
        URL.revokeObjectURL(svgUrl);
      };
      imageObj.src = svgUrl;

      // Store in project images
      if (activeProject?.project_id && agentId) {
        const fileName = `Vector Image - ${imagePrompt.slice(0, 30)}${imagePrompt.length > 30 ? '...' : ''}`;
        projectImageStorage.addGeneratedImage(
          activeProject.project_id,
          agentId,
          svgUrl,
          fileName,
          planId,
          imagePrompt,
        ).then(() => {
          window.dispatchEvent(new CustomEvent('projectImagesUpdated', { detail: { projectId: activeProject.project_id } }));
        }).catch((error) => {
          console.error('Error storing vector image:', error);
        });
      }

      trackContentEvent('image', {
        prompt: imagePrompt,
        imageStyle: selectedStyle?.option || 'none',
      });
    } catch (error: unknown) {
      console.error('Error adding SVG to canvas:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to add vector image to canvas';
      toast.error(errorMessage);
    }
  };

  const downloadSVG = () => {
    if (!svgPreview) {
      return;
    }

    const blob = new Blob([svgPreview], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `vector-image-${Date.now()}.svg`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="px-6 py-4">
      {currentStep === 'style' ? (
        <>
          <div className="mb-4">
            <h3 className="text-white font-semibold text-lg">Choose Vector Style</h3>
            <p className="text-gray-400 text-sm">Create vector images using AI with SVG output</p>
          </div>
          <div className="flex flex-col gap-4">
            <div className='flex-1'>
              <div className=" pr-2">
                {Object.entries(VectorImageStyleGroups).map(([groupKey, group], groupIndex) => (
                  <div key={groupKey} className={`${groupIndex > 0 ? 'mt-8' : ''} mb-6`}>
                    <div className="sticky top-0 pt-4 bg-neutral-900 pb-2 mb-3 z-10">
                      <h4 className="text-gray-300 text-xs font-semibold uppercase tracking-wide border-b border-neutral-700 pb-2">
                        {group.title}
                      </h4>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      {group.styles.map((style) => (
                        <button
                          key={style.option}
                          onClick={() => handleStyleSelect(style)}
                          className={`p-3 rounded-xl border-2 transition-all duration-200 text-left ${
                            selectedStyle?.option === style.option
                              ? 'border-violets-are-blue bg-violets-are-blue/10'
                              : 'border-neutral-600 hover:border-violets-are-blue bg-neutral-800 hover:bg-neutral-700'
                          }`}
                        >
                          <div className="text-white text-sm font-medium mb-1">
                            {style.label}
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </>
      ) : currentStep === 'details' ? (
        <>
          <div className="mb-4 flex items-center gap-3">
            <Button
              onClick={handleBackToStyles}
              variant="outline"
              size="sm"
              className="!px-2"
            >
              <ArrowLeft size={16} />
            </Button>
            <div>
              <h3 className="text-white font-semibold text-lg">Describe Your Vector Image</h3>
              <p className="text-gray-400 text-sm">
                Style: <span className="text-violets-are-blue">{selectedStyle?.label}</span>
              </p>
            </div>
          </div>

          <div className="space-y-6">
            <div>
              <label className="block text-gray-300 text-sm font-medium mb-2">
                Image Description
              </label>
              <TextArea
                id="vector-image-prompt"
                name="vector-image-prompt"
                ref={imagePromptRef}
                value={imagePrompt}
                onChange={(e) => setImagePrompt(e.target.value)}
                placeholder="Describe the vector image you want to create..."
                className="w-full min-h-[100px] resize-none"
                maxLength={500}
              />
              <div className="flex justify-between items-center mt-1">
                <span className="text-xs text-gray-500">
                  {imagePrompt.length}/500 characters
                </span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-gray-300 text-sm font-medium mb-2">
                  Seed: {seed}
                </label>
                <input
                  type="range"
                  min="0"
                  max="1000000"
                  step="1"
                  value={seed}
                  onChange={(e) => setSeed(Number(e.target.value))}
                  className="w-full accent-violets-are-blue"
                />
              </div>
              <div>
                <label className="block text-gray-300 text-sm font-medium mb-2">
                  Guidance: {guidanceScale}
                </label>
                <input
                  type="range"
                  min="1"
                  max="10"
                  step="0.1"
                  value={guidanceScale}
                  onChange={(e) => setGuidanceScale(Number(e.target.value))}
                  className="w-full accent-violets-are-blue"
                />
              </div>
            </div>

            {error && (
              <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
                <p className="text-red-400 text-sm">{error}</p>
              </div>
            )}

            <Button
              variant="gradient"
              size="md"
              width="w-full"
              onClick={handleGenerate}
              disabled={isGenerating || !imagePrompt.trim()}
            >
              {isGenerating ? 'Generating Vector Image...' : 'Generate Vector Image'}
            </Button>
          </div>

          {svgPreview && (
            <div className="mt-6">
              <label className="block text-gray-300 text-sm font-medium mb-2">
                Vector Preview
              </label>
              <div className="bg-neutral-800 rounded-lg p-3 border border-neutral-700">
                <div
                  className="w-full max-h-64 overflow-auto bg-white rounded"
                  dangerouslySetInnerHTML={{ __html: svgPreview }}
                />
              </div>
              <div className="flex gap-3 mt-3">
                <Button
                  variant="outline"
                  size="md"
                  onClick={downloadSVG}
                  className="flex-1"
                >
                  <Download size={16} className="mr-2" />
                  Download SVG
                </Button>
              </div>
            </div>
          )}
        </>
      ) : null}
    </div>
  );
};
